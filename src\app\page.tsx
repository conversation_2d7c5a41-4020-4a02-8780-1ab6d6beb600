'use client';

import { Suspense } from 'react';
import { motion } from 'framer-motion';
import { Terminal, Shield, Code, Zap } from 'lucide-react';
import Hero3D from '@/components/Hero3D';
import TypingEffect from '@/components/TypingEffect';

export default function Home() {
  const skills = [
    { icon: Shield, title: 'Penetration Testing', description: 'Advanced security assessments and vulnerability analysis' },
    { icon: Code, title: 'Exploit Development', description: 'Custom exploit creation and security research' },
    { icon: Terminal, title: 'Red Team Operations', description: 'Simulated attacks and security validation' },
    { icon: Zap, title: 'CTF Competitions', description: 'Capture The Flag challenges and writeups' },
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section with 3D Background */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <Suspense fallback={<div className="absolute inset-0 bg-black" />}>
          <Hero3D />
        </Suspense>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className="space-y-6"
          >
            <h1 className="text-5xl md:text-7xl font-bold matrix-text glitch">
              ArtOfVector
            </h1>
            
            <div className="text-xl md:text-2xl text-green-300 font-mono">
              <TypingEffect 
                text="Cybersecurity Specialist | Penetration Tester | Ethical Hacker"
                speed={50}
              />
            </div>
            
            <p className="text-lg text-green-200 max-w-2xl mx-auto leading-relaxed">
              Welcome to my digital realm. I specialize in cybersecurity, penetration testing, 
              and ethical hacking. Explore my latest security research, CTF writeups, and tools.
            </p>
            
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2, duration: 1 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-8"
            >
              <motion.a
                href="/blog"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-3 bg-green-400 text-black font-semibold rounded-lg hover:bg-green-300 transition-colors neon-glow"
              >
                View Writeups
              </motion.a>
              
              <motion.a
                href="/about"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-8 py-3 border border-green-400 text-green-400 font-semibold rounded-lg hover:bg-green-400/10 transition-colors"
              >
                About Me
              </motion.a>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Skills Section */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold matrix-text mb-4">Expertise</h2>
            <p className="text-green-300 text-lg">Specialized skills in cybersecurity and ethical hacking</p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {skills.map((skill, index) => {
              const Icon = skill.icon;
              return (
                <motion.div
                  key={skill.title}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  whileHover={{ y: -10 }}
                  className="bg-green-400/5 border border-green-400/20 rounded-lg p-6 text-center hover:border-green-400/40 transition-all duration-300"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-green-400/10 rounded-full mb-4">
                    <Icon className="w-8 h-8 text-green-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-green-400 mb-2">{skill.title}</h3>
                  <p className="text-green-300 text-sm">{skill.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Latest Posts Preview */}
      <section className="py-20 px-4 bg-green-400/5">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold matrix-text mb-4">Latest Writeups</h2>
            <p className="text-green-300 text-lg">Recent security research and CTF solutions</p>
          </motion.div>

          <div className="text-center">
            <motion.a
              href="/blog"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-6 py-3 border border-green-400 text-green-400 font-semibold rounded-lg hover:bg-green-400/10 transition-colors"
            >
              <Terminal className="w-5 h-5 mr-2" />
              Explore All Posts
            </motion.a>
          </div>
        </div>
      </section>
    </div>
  );
}
