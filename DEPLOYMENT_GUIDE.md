# 🚀 Deployment Guide for ArtOfVector Website

## Quick Deployment Steps

### Option 1: Using GitHub Desktop (Recommended)

1. **Open GitHub Desktop**
   - If you don't have it, download from: https://desktop.github.com/

2. **Add Repository**
   - Click "Add an Existing Repository from your hard drive"
   - Select the folder: `C:\Users\<USER>\Documents\GitHub\artofvector`

3. **Commit Changes**
   - You'll see all the new files in the changes tab
   - Add commit message: "Initial cybersecurity portfolio website"
   - Click "Commit to main"

4. **Publish Repository**
   - Click "Publish repository"
   - Repository name: `artofvector`
   - Description: "Professional cybersecurity portfolio with 3D graphics and blog"
   - Make sure "Keep this code private" is unchecked (for GitHub Pages)
   - Click "Publish Repository"

5. **Enable GitHub Pages**
   - Go to your repository on GitHub.com
   - Click Settings tab
   - Scroll down to "Pages" in the left sidebar
   - Under "Source", select "GitHub Actions"
   - Your site will be available at: `https://yourusername.github.io/artofvector`

### Option 2: Using Command Line (If Git is available)

```bash
# Initialize repository (if not already done)
git init

# Add all files
git add .

# Commit changes
git commit -m "Initial cybersecurity portfolio website"

# Add remote repository
git remote add origin https://github.com/yourusername/artofvector.git

# Push to GitHub
git push -u origin main
```

### Option 3: Manual Upload via GitHub Web Interface

1. **Create New Repository**
   - Go to GitHub.com
   - Click "New repository"
   - Name: `artofvector`
   - Description: "Professional cybersecurity portfolio"
   - Public repository
   - Don't initialize with README (we already have one)

2. **Upload Files**
   - Click "uploading an existing file"
   - Drag and drop all files from your project folder
   - Commit message: "Initial cybersecurity portfolio website"
   - Click "Commit changes"

## 🔧 Pre-Deployment Checklist

✅ All files created and configured:
- ✅ Homepage with 3D animations
- ✅ About page with professional content
- ✅ Blog system with sample posts
- ✅ Tools showcase page
- ✅ Navigation component
- ✅ Responsive design
- ✅ GitHub Actions workflow
- ✅ Next.js configuration for static export
- ✅ README documentation

## 📁 Files to Deploy

Make sure these files are included:

### Core Application
- `src/app/page.tsx` - Homepage
- `src/app/layout.tsx` - Main layout
- `src/app/globals.css` - Styles
- `src/app/about/page.tsx` - About page
- `src/app/blog/page.tsx` - Blog listing
- `src/app/blog/[slug]/page.tsx` - Blog posts
- `src/app/tools/page.tsx` - Tools page

### Components
- `src/components/Navigation.tsx`
- `src/components/Hero3D.tsx`
- `src/components/TypingEffect.tsx`

### Configuration
- `package.json` - Dependencies
- `next.config.ts` - Next.js config
- `tailwind.config.js` - Tailwind config
- `tsconfig.json` - TypeScript config

### Deployment
- `.github/workflows/deploy.yml` - GitHub Actions
- `README.md` - Documentation

## 🌐 After Deployment

1. **Wait for Build**
   - GitHub Actions will automatically build and deploy
   - Check the "Actions" tab for build status
   - First build may take 3-5 minutes

2. **Access Your Site**
   - Your site will be live at: `https://yourusername.github.io/artofvector`
   - It may take a few minutes to propagate

3. **Custom Domain (Optional)**
   - Add a CNAME file with your domain
   - Configure DNS settings
   - Update `next.config.ts` with your domain

## 🎯 Customization After Deployment

### Personal Information
- Update `src/app/about/page.tsx` with your real information
- Modify social links in `src/components/Navigation.tsx`
- Add your actual achievements and certifications

### Blog Content
- Replace sample posts with your real writeups
- Add new posts by creating files in the blog structure
- Update post metadata and content

### Tools Section
- Add your actual security tools and projects
- Update GitHub links to your real repositories
- Modify tool descriptions and technologies

## 🔍 Troubleshooting

### Build Fails
- Check the Actions tab for error details
- Ensure all dependencies are in package.json
- Verify TypeScript types are correct

### Site Not Loading
- Wait 5-10 minutes after first deployment
- Check GitHub Pages settings
- Verify the repository is public

### 3D Animations Not Working
- Ensure Three.js dependencies are installed
- Check browser console for WebGL errors
- Verify component imports are correct

## 📞 Support

If you encounter issues:
1. Check the GitHub Actions logs
2. Verify all files are committed
3. Ensure repository is public for GitHub Pages
4. Check browser console for errors

Your professional cybersecurity website is ready to showcase your expertise! 🎉
