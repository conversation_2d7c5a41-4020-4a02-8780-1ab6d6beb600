'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { Calendar, Clock, Tag, ArrowRight } from 'lucide-react';

export default function Blog() {
  // Sample blog posts - in a real app, this would come from a CMS or markdown files
  const posts = [
    {
      slug: 'sql-injection-advanced-techniques',
      title: 'Advanced SQL Injection Techniques: Beyond UNION-based Attacks',
      excerpt: 'Exploring advanced SQL injection techniques including blind SQL injection, time-based attacks, and bypassing modern WAF protections.',
      date: '2024-01-15',
      readTime: '12 min read',
      tags: ['SQL Injection', 'Web Security', 'Penetration Testing'],
      featured: true
    },
    {
      slug: 'buffer-overflow-exploitation',
      title: 'Buffer Overflow Exploitation: From Theory to Practice',
      excerpt: 'A comprehensive guide to understanding and exploiting buffer overflow vulnerabilities in modern applications.',
      date: '2024-01-10',
      readTime: '18 min read',
      tags: ['Binary Exploitation', 'Reverse Engineering', 'CTF'],
      featured: false
    },
    {
      slug: 'active-directory-enumeration',
      title: 'Active Directory Enumeration and Attack Vectors',
      excerpt: 'Deep dive into Active Directory security, common misconfigurations, and attack techniques used by red teams.',
      date: '2024-01-05',
      readTime: '15 min read',
      tags: ['Active Directory', 'Red Team', 'Windows Security'],
      featured: false
    },
    {
      slug: 'web-application-firewall-bypass',
      title: 'WAF Bypass Techniques: A Practical Approach',
      excerpt: 'Learn how to identify and bypass various Web Application Firewalls using encoding, obfuscation, and other techniques.',
      date: '2023-12-28',
      readTime: '10 min read',
      tags: ['WAF Bypass', 'Web Security', 'Evasion'],
      featured: false
    },
    {
      slug: 'privilege-escalation-linux',
      title: 'Linux Privilege Escalation: Common Vectors and Mitigation',
      excerpt: 'Comprehensive overview of Linux privilege escalation techniques and how to defend against them.',
      date: '2023-12-20',
      readTime: '14 min read',
      tags: ['Linux', 'Privilege Escalation', 'Post-Exploitation'],
      featured: false
    },
    {
      slug: 'api-security-testing',
      title: 'API Security Testing: Modern Approaches and Tools',
      excerpt: 'Best practices for testing REST and GraphQL APIs, including authentication bypass and injection attacks.',
      date: '2023-12-15',
      readTime: '11 min read',
      tags: ['API Security', 'Web Security', 'Testing'],
      featured: false
    }
  ];

  const featuredPost = posts.find(post => post.featured);
  const regularPosts = posts.filter(post => !post.featured);

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold matrix-text mb-6">
              Security Blog
            </h1>
            <p className="text-xl text-green-300 mb-8">
              Writeups, tutorials, and insights from the world of cybersecurity
            </p>
          </motion.div>
        </div>
      </section>

      {/* Featured Post */}
      {featuredPost && (
        <section className="py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-8"
            >
              <h2 className="text-3xl font-bold text-green-400 mb-8">Featured Post</h2>
            </motion.div>

            <motion.article
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-green-400/5 border border-green-400/20 rounded-lg p-8 hover:border-green-400/40 transition-all duration-300"
            >
              <div className="flex flex-wrap gap-2 mb-4">
                {featuredPost.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-3 py-1 bg-green-400/10 border border-green-400/20 rounded-full text-green-400 text-sm"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              <h3 className="text-2xl md:text-3xl font-bold text-green-400 mb-4 hover:text-green-300 transition-colors">
                <Link href={`/blog/${featuredPost.slug}`}>
                  {featuredPost.title}
                </Link>
              </h3>

              <p className="text-green-200 mb-6 leading-relaxed">
                {featuredPost.excerpt}
              </p>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6 text-green-300 text-sm">
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-2" />
                    {new Date(featuredPost.date).toLocaleDateString()}
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    {featuredPost.readTime}
                  </div>
                </div>

                <Link
                  href={`/blog/${featuredPost.slug}`}
                  className="inline-flex items-center text-green-400 hover:text-green-300 transition-colors"
                >
                  Read More
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </div>
            </motion.article>
          </div>
        </section>
      )}

      {/* All Posts */}
      <section className="py-16 px-4 bg-green-400/5">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold text-green-400 mb-8">All Posts</h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularPosts.map((post, index) => (
              <motion.article
                key={post.slug}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className="bg-black border border-green-400/20 rounded-lg p-6 hover:border-green-400/40 transition-all duration-300 group"
              >
                <div className="flex flex-wrap gap-2 mb-4">
                  {post.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-green-400/10 border border-green-400/20 rounded text-green-400 text-xs"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <h3 className="text-xl font-bold text-green-400 mb-3 group-hover:text-green-300 transition-colors">
                  <Link href={`/blog/${post.slug}`}>
                    {post.title}
                  </Link>
                </h3>

                <p className="text-green-200 mb-4 text-sm leading-relaxed">
                  {post.excerpt}
                </p>

                <div className="flex items-center justify-between text-green-300 text-xs">
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {new Date(post.date).toLocaleDateString()}
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {post.readTime}
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
