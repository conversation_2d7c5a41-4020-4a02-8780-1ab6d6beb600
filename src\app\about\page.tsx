'use client';

import { motion } from 'framer-motion';
import { Shield, Code, Terminal, Award, BookOpen, Users } from 'lucide-react';

export default function About() {
  const achievements = [
    { icon: Shield, title: 'OSCP Certified', description: 'Offensive Security Certified Professional' },
    { icon: Code, title: 'Bug Bounty Hunter', description: 'Active researcher on major platforms' },
    { icon: Terminal, title: 'CTF Champion', description: 'Multiple competition victories' },
    { icon: Award, title: 'Security Researcher', description: 'Published CVEs and security advisories' },
  ];

  const skills = [
    'Penetration Testing', 'Web Application Security', 'Network Security',
    'Reverse Engineering', 'Exploit Development', 'Social Engineering',
    'Incident Response', 'Malware Analysis', 'Cryptography', 'Cloud Security'
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold matrix-text mb-6">
              About Me
            </h1>
            <p className="text-xl text-green-300 mb-8">
              Cybersecurity Professional & Ethical Hacker
            </p>
          </motion.div>
        </div>
      </section>

      {/* Bio Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-6"
            >
              <h2 className="text-3xl font-bold text-green-400 mb-6">My Journey</h2>
              <p className="text-green-200 leading-relaxed">
                I'm a passionate cybersecurity professional with over 5 years of experience in 
                penetration testing, vulnerability assessment, and security research. My journey 
                began with a curiosity about how systems work and evolved into a career dedicated 
                to making the digital world safer.
              </p>
              <p className="text-green-200 leading-relaxed">
                I specialize in web application security, network penetration testing, and 
                exploit development. When I'm not breaking things professionally, you can find 
                me participating in CTF competitions or researching the latest security vulnerabilities.
              </p>
              <p className="text-green-200 leading-relaxed">
                Through this blog, I share my knowledge, document my findings, and contribute 
                to the cybersecurity community. Every writeup represents hours of research, 
                testing, and learning.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-green-400/5 border border-green-400/20 rounded-lg p-8"
            >
              <h3 className="text-2xl font-bold text-green-400 mb-6">Quick Stats</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-green-300">Years of Experience</span>
                  <span className="text-green-400 font-bold">5+</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-300">CTF Competitions</span>
                  <span className="text-green-400 font-bold">50+</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-300">Security Assessments</span>
                  <span className="text-green-400 font-bold">100+</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-300">Bug Bounties</span>
                  <span className="text-green-400 font-bold">25+</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Achievements Section */}
      <section className="py-16 px-4 bg-green-400/5">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold matrix-text mb-4">Achievements</h2>
            <p className="text-green-300 text-lg">Certifications and accomplishments</p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => {
              const Icon = achievement.icon;
              return (
                <motion.div
                  key={achievement.title}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className="bg-black border border-green-400/20 rounded-lg p-6 text-center hover:border-green-400/40 transition-all duration-300"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-green-400/10 rounded-full mb-4">
                    <Icon className="w-8 h-8 text-green-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-green-400 mb-2">{achievement.title}</h3>
                  <p className="text-green-300 text-sm">{achievement.description}</p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Skills Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold matrix-text mb-4">Technical Skills</h2>
            <p className="text-green-300 text-lg">Areas of expertise and specialization</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="flex flex-wrap justify-center gap-4"
          >
            {skills.map((skill, index) => (
              <motion.span
                key={skill}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                className="px-4 py-2 bg-green-400/10 border border-green-400/20 rounded-full text-green-400 hover:bg-green-400/20 transition-all duration-300"
              >
                {skill}
              </motion.span>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 px-4 bg-green-400/5">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h2 className="text-4xl font-bold matrix-text mb-6">Let's Connect</h2>
            <p className="text-green-300 text-lg mb-8">
              Interested in collaboration or have questions about cybersecurity?
            </p>
            <motion.a
              href="mailto:<EMAIL>"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center px-8 py-3 bg-green-400 text-black font-semibold rounded-lg hover:bg-green-300 transition-colors neon-glow"
            >
              <Terminal className="w-5 h-5 mr-2" />
              Get In Touch
            </motion.a>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
