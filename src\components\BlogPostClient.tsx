'use client';

import { motion } from 'framer-motion';
import { Calendar, Clock, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface BlogPost {
  title: string;
  date: string;
  readTime: string;
  tags: string[];
  content: string;
}

interface BlogPostClientProps {
  post: BlogPost | null;
}

const BlogPostClient = ({ post }: BlogPostClientProps) => {
  if (!post) {
    return (
      <div className="min-h-screen pt-16 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-green-400 mb-4">Post Not Found</h1>
          <p className="text-green-300 mb-8">The blog post you're looking for doesn't exist.</p>
          <Link
            href="/blog"
            className="inline-flex items-center px-6 py-3 border border-green-400 text-green-400 font-semibold rounded-lg hover:bg-green-400/10 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Blog
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16">
      {/* Header */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Link
              href="/blog"
              className="inline-flex items-center text-green-400 hover:text-green-300 transition-colors mb-8"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Link>

            <div className="flex flex-wrap gap-2 mb-6">
              {post.tags.map((tag: string) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-green-400/10 border border-green-400/20 rounded-full text-green-400 text-sm"
                >
                  {tag}
                </span>
              ))}
            </div>

            <h1 className="text-4xl md:text-5xl font-bold matrix-text mb-6">
              {post.title}
            </h1>

            <div className="flex items-center space-x-6 text-green-300 mb-8">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-2" />
                {new Date(post.date).toLocaleDateString()}
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2" />
                {post.readTime}
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Content */}
      <section className="pb-20 px-4">
        <div className="max-w-4xl mx-auto">
          <motion.article
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="prose prose-invert prose-green max-w-none"
          >
            <div 
              className="text-green-200 leading-relaxed"
              dangerouslySetInnerHTML={{ 
                __html: post.content
                  .replace(/\n/g, '<br>')
                  .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre class="bg-green-400/5 border border-green-400/20 rounded-lg p-4 overflow-x-auto"><code class="text-green-400">$2</code></pre>')
                  .replace(/`([^`]+)`/g, '<code class="bg-green-400/10 px-2 py-1 rounded text-green-400">$1</code>')
                  .replace(/^# (.*$)/gm, '<h1 class="text-3xl font-bold text-green-400 mt-8 mb-4">$1</h1>')
                  .replace(/^## (.*$)/gm, '<h2 class="text-2xl font-bold text-green-400 mt-6 mb-3">$1</h2>')
                  .replace(/^### (.*$)/gm, '<h3 class="text-xl font-bold text-green-400 mt-4 mb-2">$1</h3>')
              }}
            />
          </motion.article>
        </div>
      </section>
    </div>
  );
};

export default BlogPostClient;
