{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/artofvector/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { Terminal, Shield, FileText, User, Github, Linkedin, Twitter } from 'lucide-react';\n\nconst Navigation = () => {\n  const pathname = usePathname();\n\n  const navItems = [\n    { href: '/', label: 'Home', icon: Terminal },\n    { href: '/about', label: 'About', icon: User },\n    { href: '/blog', label: 'Blog', icon: FileText },\n    { href: '/tools', label: 'Tools', icon: Shield },\n  ];\n\n  const socialLinks = [\n    { href: 'https://github.com', icon: Github, label: 'GitHub' },\n    { href: 'https://linkedin.com', icon: Linkedin, label: 'LinkedIn' },\n    { href: 'https://twitter.com', icon: Twitter, label: 'Twitter' },\n  ];\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className=\"fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-sm border-b border-green-400/20\"\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <motion.div\n              whileHover={{ rotate: 360 }}\n              transition={{ duration: 0.5 }}\n              className=\"text-green-400\"\n            >\n              <Terminal className=\"w-8 h-8\" />\n            </motion.div>\n            <span className=\"text-xl font-bold matrix-text group-hover:text-green-300 transition-colors\">\n              ArtOfVector\n            </span>\n          </Link>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              \n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${\n                    isActive\n                      ? 'text-green-400 bg-green-400/10 border border-green-400/20'\n                      : 'text-green-300 hover:text-green-400 hover:bg-green-400/5'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{item.label}</span>\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Social Links */}\n          <div className=\"flex items-center space-x-4\">\n            {socialLinks.map((social) => {\n              const Icon = social.icon;\n              return (\n                <motion.a\n                  key={social.href}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"text-green-300 hover:text-green-400 transition-colors\"\n                  aria-label={social.label}\n                >\n                  <Icon className=\"w-5 h-5\" />\n                </motion.a>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <div className=\"md:hidden border-t border-green-400/20\">\n        <div className=\"px-2 pt-2 pb-3 space-y-1\">\n          {navItems.map((item) => {\n            const Icon = item.icon;\n            const isActive = pathname === item.href;\n            \n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-all duration-200 ${\n                  isActive\n                    ? 'text-green-400 bg-green-400/10 border border-green-400/20'\n                    : 'text-green-300 hover:text-green-400 hover:bg-green-400/5'\n                }`}\n              >\n                <Icon className=\"w-5 h-5\" />\n                <span>{item.label}</span>\n              </Link>\n            );\n          })}\n        </div>\n      </div>\n    </motion.nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAOA,MAAM,aAAa;IACjB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAC3C;YAAE,MAAM;YAAU,OAAO;YAAS,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC7C;YAAE,MAAM;YAAS,OAAO;YAAQ,MAAM,8MAAA,CAAA,WAAQ;QAAC;QAC/C;YAAE,MAAM;YAAU,OAAO;YAAS,MAAM,sMAAA,CAAA,SAAM;QAAC;KAChD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM;YAAsB,MAAM,sMAAA,CAAA,SAAM;YAAE,OAAO;QAAS;QAC5D;YAAE,MAAM;YAAwB,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO;QAAW;QAClE;YAAE,MAAM;YAAuB,MAAM,wMAAA,CAAA,UAAO;YAAE,OAAO;QAAU;KAChE;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,QAAQ;oCAAI;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAM/F,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,iGAAiG,EAC3G,WACI,8DACA,4DACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAM,KAAK,KAAK;;;;;;;mCATZ,KAAK,IAAI;;;;;4BAYpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,MAAM,OAAO,OAAO,IAAI;gCACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,OAAO,IAAI;oCACjB,QAAO;oCACP,KAAI;oCACJ,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,cAAY,OAAO,KAAK;8CAExB,cAAA,8OAAC;wCAAK,WAAU;;;;;;mCATX,OAAO,IAAI;;;;;4BAYtB;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wBAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAC,mGAAmG,EAC7G,WACI,8DACA,4DACJ;;8CAEF,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,KAAK,KAAK;;;;;;;2BATZ,KAAK,IAAI;;;;;oBAYpB;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}]}