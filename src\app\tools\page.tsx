'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Terminal, Shield, Code, Search, Lock, Zap, ExternalLink, Github } from 'lucide-react';

export default function Tools() {
  const tools = [
    {
      name: 'SQLMap Automation Script',
      description: 'Automated SQL injection testing script with custom payloads and WAF bypass techniques.',
      category: 'Web Security',
      icon: Shield,
      technologies: ['Python', 'SQLMap', 'Bash'],
      github: 'https://github.com/artofvector/sqlmap-automation',
      demo: null,
      featured: true
    },
    {
      name: 'Directory Bruteforcer',
      description: 'High-performance directory and file discovery tool with custom wordlists.',
      category: 'Reconnaissance',
      icon: Search,
      technologies: ['Go', 'Concurrency', 'HTTP'],
      github: 'https://github.com/artofvector/dir-bruteforce',
      demo: null,
      featured: false
    },
    {
      name: 'Privilege Escalation Checker',
      description: 'Linux privilege escalation enumeration script for penetration testing.',
      category: 'Post-Exploitation',
      icon: Lock,
      technologies: ['Bash', 'Linux', 'SUID'],
      github: 'https://github.com/artofvector/privesc-checker',
      demo: null,
      featured: false
    },
    {
      name: 'Payload Generator',
      description: 'Multi-format payload generator for various injection attacks and bypasses.',
      category: 'Exploitation',
      icon: Code,
      technologies: ['Python', 'Jinja2', 'CLI'],
      github: 'https://github.com/artofvector/payload-gen',
      demo: 'https://tools.artofvector.com/payload-gen',
      featured: true
    },
    {
      name: 'Network Scanner',
      description: 'Fast network discovery and port scanning tool with service detection.',
      category: 'Reconnaissance',
      icon: Zap,
      technologies: ['Python', 'Asyncio', 'Nmap'],
      github: 'https://github.com/artofvector/net-scanner',
      demo: null,
      featured: false
    },
    {
      name: 'Hash Cracker',
      description: 'Multi-threaded hash cracking tool supporting various algorithms.',
      category: 'Cryptography',
      icon: Terminal,
      technologies: ['C++', 'OpenMP', 'Hashcat'],
      github: 'https://github.com/artofvector/hash-cracker',
      demo: null,
      featured: false
    }
  ];

  const categories = ['All', 'Web Security', 'Reconnaissance', 'Post-Exploitation', 'Exploitation', 'Cryptography'];
  const [selectedCategory, setSelectedCategory] = useState('All');

  const filteredTools = selectedCategory === 'All' 
    ? tools 
    : tools.filter(tool => tool.category === selectedCategory);

  const featuredTools = tools.filter(tool => tool.featured);

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold matrix-text mb-6">
              Security Tools
            </h1>
            <p className="text-xl text-green-300 mb-8">
              Custom tools and scripts for penetration testing and security research
            </p>
          </motion.div>
        </div>
      </section>

      {/* Featured Tools */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold text-green-400 mb-8">Featured Tools</h2>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {featuredTools.map((tool, index) => {
              const Icon = tool.icon;
              return (
                <motion.div
                  key={tool.name}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className="bg-green-400/5 border border-green-400/20 rounded-lg p-8 hover:border-green-400/40 transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center">
                      <div className="inline-flex items-center justify-center w-12 h-12 bg-green-400/10 rounded-lg mr-4">
                        <Icon className="w-6 h-6 text-green-400" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-green-400">{tool.name}</h3>
                        <span className="text-green-300 text-sm">{tool.category}</span>
                      </div>
                    </div>
                  </div>

                  <p className="text-green-200 mb-6 leading-relaxed">
                    {tool.description}
                  </p>

                  <div className="flex flex-wrap gap-2 mb-6">
                    {tool.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-3 py-1 bg-green-400/10 border border-green-400/20 rounded-full text-green-400 text-sm"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>

                  <div className="flex space-x-4">
                    <a
                      href={tool.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 bg-green-400 text-black font-semibold rounded-lg hover:bg-green-300 transition-colors"
                    >
                      <Github className="w-4 h-4 mr-2" />
                      View Code
                    </a>
                    {tool.demo && (
                      <a
                        href={tool.demo}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 border border-green-400 text-green-400 font-semibold rounded-lg hover:bg-green-400/10 transition-colors"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Live Demo
                      </a>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* All Tools */}
      <section className="py-16 px-4 bg-green-400/5">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold text-green-400 mb-8">All Tools</h2>
            
            {/* Category Filter */}
            <div className="flex flex-wrap gap-2 mb-8">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-300 ${
                    selectedCategory === category
                      ? 'bg-green-400 text-black'
                      : 'bg-green-400/10 border border-green-400/20 text-green-400 hover:bg-green-400/20'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTools.map((tool, index) => {
              const Icon = tool.icon;
              return (
                <motion.div
                  key={tool.name}
                  initial={{ opacity: 0, y: 50 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  className="bg-black border border-green-400/20 rounded-lg p-6 hover:border-green-400/40 transition-all duration-300"
                >
                  <div className="flex items-center mb-4">
                    <div className="inline-flex items-center justify-center w-10 h-10 bg-green-400/10 rounded-lg mr-3">
                      <Icon className="w-5 h-5 text-green-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-green-400">{tool.name}</h3>
                      <span className="text-green-300 text-sm">{tool.category}</span>
                    </div>
                  </div>

                  <p className="text-green-200 mb-4 text-sm leading-relaxed">
                    {tool.description}
                  </p>

                  <div className="flex flex-wrap gap-1 mb-4">
                    {tool.technologies.slice(0, 2).map((tech) => (
                      <span
                        key={tech}
                        className="px-2 py-1 bg-green-400/10 border border-green-400/20 rounded text-green-400 text-xs"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>

                  <div className="flex space-x-2">
                    <a
                      href={tool.github}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 inline-flex items-center justify-center px-3 py-2 bg-green-400/10 border border-green-400/20 text-green-400 font-medium rounded hover:bg-green-400/20 transition-colors text-sm"
                    >
                      <Github className="w-3 h-3 mr-1" />
                      Code
                    </a>
                    {tool.demo && (
                      <a
                        href={tool.demo}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex-1 inline-flex items-center justify-center px-3 py-2 bg-green-400 text-black font-medium rounded hover:bg-green-300 transition-colors text-sm"
                      >
                        <ExternalLink className="w-3 h-3 mr-1" />
                        Demo
                      </a>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>
    </div>
  );
}
