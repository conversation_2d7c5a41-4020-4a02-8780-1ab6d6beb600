# ArtOfVector - Cybersecurity Portfolio & Blog

A modern, professional cybersecurity portfolio website featuring 3D motion graphics, a comprehensive blog system for security writeups, and an interactive tools showcase. Built with Next.js, Three.js, and Tailwind CSS.

## 🚀 Features

- **Modern 3D Graphics**: Interactive 3D animations using Three.js and React Three Fiber
- **Professional Design**: Dark cybersecurity-themed design with matrix-style effects
- **Blog System**: Comprehensive blog for security writeups and CTF solutions
- **Tools Showcase**: Interactive gallery of custom security tools and scripts
- **Responsive Design**: Fully responsive across all devices
- **GitHub Pages Ready**: Configured for easy deployment to GitHub Pages
- **SEO Optimized**: Proper meta tags and structured data

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS 4
- **3D Graphics**: Three.js with React Three Fiber
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Deployment**: GitHub Pages with GitHub Actions

## 🎨 Design Features

- Matrix-style terminal effects
- Neon glow animations
- Glitch text effects
- Animated 3D particle systems
- Professional cybersecurity color scheme (black/green)
- Custom scrollbars and UI elements

## 📁 Project Structure

```
src/
├── app/
│   ├── about/          # About page
│   ├── blog/           # Blog listing and individual posts
│   ├── tools/          # Tools showcase
│   ├── layout.tsx      # Root layout with navigation
│   ├── page.tsx        # Homepage with 3D hero
│   └── globals.css     # Global styles and animations
├── components/
│   ├── Navigation.tsx  # Main navigation component
│   ├── Hero3D.tsx      # 3D animated hero section
│   └── TypingEffect.tsx # Terminal typing animation
└── public/             # Static assets
```

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/artofvector.git
   cd artofvector
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📝 Customization

### Personal Information
- Update `src/app/layout.tsx` for site metadata
- Modify `src/app/about/page.tsx` for personal information
- Edit `src/components/Navigation.tsx` for social links

### Blog Posts
- Add new posts in `src/app/blog/[slug]/page.tsx`
- Update the posts array in `src/app/blog/page.tsx`
- For a full CMS, consider integrating with Contentful or Sanity

### Tools
- Update the tools array in `src/app/tools/page.tsx`
- Add GitHub links and demo URLs
- Customize categories and technologies

## 🚀 Deployment

### GitHub Pages (Recommended)

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Enable GitHub Pages**
   - Go to repository Settings > Pages
   - Select "GitHub Actions" as source
   - The site will automatically deploy on push to main

3. **Custom Domain (Optional)**
   - Update `next.config.ts` with your domain
   - Add CNAME file in public folder
   - Configure DNS settings

### Manual Deployment

```bash
npm run build
npm run export
```

The static files will be in the `out` directory.

## 🎯 Use Cases

- **Cybersecurity Professionals**: Showcase skills and experience
- **Penetration Testers**: Document findings and methodologies
- **CTF Players**: Share writeups and solutions
- **Security Researchers**: Publish tools and discoveries
- **Students**: Build a professional portfolio

## 🔧 Development

### Adding New Pages
1. Create a new directory in `src/app/`
2. Add a `page.tsx` file
3. Update navigation in `src/components/Navigation.tsx`

### Customizing Animations
- Modify `src/components/Hero3D.tsx` for 3D effects
- Update `src/app/globals.css` for CSS animations
- Adjust Framer Motion settings in components

### Styling
- Colors and themes in `src/app/globals.css`
- Component-specific styles using Tailwind classes
- Custom animations and effects

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Contact

- **Email**: <EMAIL>
- **GitHub**: [@artofvector](https://github.com/artofvector)
- **LinkedIn**: [ArtOfVector](https://linkedin.com/in/artofvector)

---

**Note**: This template is designed for educational and professional purposes. Always ensure you have proper authorization before conducting any security testing.
