@import "tailwindcss";

:root {
  --background: #000000;
  --foreground: #ff4444;
  --accent: #ff4444;
  --secondary: #0a0a0a;
  --border: #1a1a1a;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-jetbrains-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #0a0a0a;
}

::-webkit-scrollbar-thumb {
  background: #ff4444;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ff6666;
}

/* Matrix-like text effect */
.matrix-text {
  font-family: var(--font-jetbrains-mono), monospace;
  text-shadow: 0 0 10px #ff4444;
}

/* Glitch effect */
.glitch {
  position: relative;
  animation: glitch 2s infinite;
}

@keyframes glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

/* Terminal cursor */
.terminal-cursor::after {
  content: '█';
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Neon glow effect */
.neon-glow {
  box-shadow: 0 0 20px #ff4444, 0 0 40px #ff4444, 0 0 60px #ff4444;
}

/* Code block styling */
pre {
  background: #0a0a0a;
  border: 1px solid #1a1a1a;
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
  font-family: var(--font-jetbrains-mono), monospace;
}

code {
  font-family: var(--font-jetbrains-mono), monospace;
  background: #0a0a0a;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  border: 1px solid #1a1a1a;
}
