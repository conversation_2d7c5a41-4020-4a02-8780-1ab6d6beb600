{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/artofvector/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Terminal, Shield, FileText, User, Github, Linkedin, Twitter, Menu, X } from 'lucide-react';\n\nconst Navigation = () => {\n  const pathname = usePathname();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  const navItems = [\n    { href: '/', label: 'Home', icon: Terminal },\n    { href: '/about', label: 'About', icon: User },\n    { href: '/blog', label: 'Blog', icon: FileText },\n    { href: '/tools', label: 'Tools', icon: Shield },\n  ];\n\n  const socialLinks = [\n    { href: 'https://github.com', icon: Github, label: 'GitHub' },\n    { href: 'https://linkedin.com', icon: Linkedin, label: 'LinkedIn' },\n    { href: 'https://twitter.com', icon: Twitter, label: 'Twitter' },\n  ];\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className=\"fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-sm border-b border-red-400/20\"\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <motion.div\n              whileHover={{ rotate: 360 }}\n              transition={{ duration: 0.5 }}\n              className=\"text-red-400\"\n            >\n              <Terminal className=\"w-8 h-8\" />\n            </motion.div>\n            <span className=\"text-xl font-bold matrix-text group-hover:text-red-300 transition-colors\">\n              ArtOfVector\n            </span>\n          </Link>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              \n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${\n                    isActive\n                      ? 'text-red-400 bg-red-400/10 border border-red-400/20'\n                      : 'text-red-300 hover:text-red-400 hover:bg-red-400/5'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{item.label}</span>\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"text-red-400 hover:text-red-300 transition-colors\"\n              aria-label=\"Toggle mobile menu\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n\n          {/* Desktop Social Links */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {socialLinks.map((social) => {\n              const Icon = social.icon;\n              return (\n                <motion.a\n                  key={social.href}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"text-red-300 hover:text-red-400 transition-colors\"\n                  aria-label={social.label}\n                >\n                  <Icon className=\"w-5 h-5\" />\n                </motion.a>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden border-t border-red-400/20 bg-black/95 backdrop-blur-sm\"\n          >\n            <div className=\"px-4 pt-2 pb-3 space-y-1\">\n              {navItems.map((item, index) => {\n                const Icon = item.icon;\n                const isActive = pathname === item.href;\n\n                return (\n                  <motion.div\n                    key={item.href}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => setIsMobileMenuOpen(false)}\n                      className={`flex items-center space-x-3 px-3 py-3 rounded-md text-base font-medium transition-all duration-200 ${\n                        isActive\n                          ? 'text-red-400 bg-red-400/10 border border-red-400/20'\n                          : 'text-red-300 hover:text-red-400 hover:bg-red-400/5'\n                      }`}\n                    >\n                      <Icon className=\"w-5 h-5\" />\n                      <span>{item.label}</span>\n                    </Link>\n                  </motion.div>\n                );\n              })}\n\n              {/* Mobile Social Links */}\n              <div className=\"flex justify-center space-x-6 pt-4 pb-2 border-t border-red-400/20 mt-4\">\n                {socialLinks.map((social) => {\n                  const Icon = social.icon;\n                  return (\n                    <motion.a\n                      key={social.href}\n                      href={social.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.95 }}\n                      className=\"text-red-300 hover:text-red-400 transition-colors\"\n                      aria-label={social.label}\n                    >\n                      <Icon className=\"w-5 h-5\" />\n                    </motion.a>\n                  );\n                })}\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQA,MAAM,aAAa;;IACjB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM,6MAAA,CAAA,WAAQ;QAAC;QAC3C;YAAE,MAAM;YAAU,OAAO;YAAS,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC7C;YAAE,MAAM;YAAS,OAAO;YAAQ,MAAM,iNAAA,CAAA,WAAQ;QAAC;QAC/C;YAAE,MAAM;YAAU,OAAO;YAAS,MAAM,yMAAA,CAAA,SAAM;QAAC;KAChD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM;YAAsB,MAAM,yMAAA,CAAA,SAAM;YAAE,OAAO;QAAS;QAC5D;YAAE,MAAM;YAAwB,MAAM,6MAAA,CAAA,WAAQ;YAAE,OAAO;QAAW;QAClE;YAAE,MAAM;YAAuB,MAAM,2MAAA,CAAA,UAAO;YAAE,OAAO;QAAU;KAChE;IAED,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,QAAQ;oCAAI;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCAAK,WAAU;8CAA2E;;;;;;;;;;;;sCAM7F,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,iGAAiG,EAC3G,WACI,wDACA,sDACJ;;sDAEF,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;sDAAM,KAAK,KAAK;;;;;;;mCATZ,KAAK,IAAI;;;;;4BAYpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;gCACV,cAAW;0CAEV,iCACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAMtB,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,MAAM,OAAO,OAAO,IAAI;gCACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,OAAO,IAAI;oCACjB,QAAO;oCACP,KAAI;oCACJ,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;oCACV,cAAY,OAAO,KAAK;8CAExB,cAAA,6LAAC;wCAAK,WAAU;;;;;;mCATX,OAAO,IAAI;;;;;4BAYtB;;;;;;;;;;;;;;;;;0BAMN,6LAAC,4LAAA,CAAA,kBAAe;0BACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,MAAM;gCACnB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC,mGAAmG,EAC7G,WACI,wDACA,sDACJ;;0DAEF,6LAAC;gDAAK,WAAU;;;;;;0DAChB,6LAAC;0DAAM,KAAK,KAAK;;;;;;;;;;;;mCAfd,KAAK,IAAI;;;;;4BAmBpB;0CAGA,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC;oCAChB,MAAM,OAAO,OAAO,IAAI;oCACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,OAAO,IAAI;wCACjB,QAAO;wCACP,KAAI;wCACJ,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;wCACV,cAAY,OAAO,KAAK;kDAExB,cAAA,6LAAC;4CAAK,WAAU;;;;;;uCATX,OAAO,IAAI;;;;;gCAYtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;GApKM;;QACa,qIAAA,CAAA,cAAW;;;KADxB;uCAsKS", "debugId": null}}]}