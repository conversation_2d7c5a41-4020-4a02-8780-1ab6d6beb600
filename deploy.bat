@echo off
echo 🚀 ArtOfVector Website Deployment Script
echo ========================================

echo.
echo Checking if Git is available...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git is not available in PATH
    echo Please use GitHub Desktop or manual upload method
    echo See DEPLOYMENT_GUIDE.md for instructions
    pause
    exit /b 1
)

echo ✅ Git is available

echo.
echo Adding all files to Git...
git add .

echo.
echo Committing changes...
set /p commit_message="Enter commit message (or press Enter for default): "
if "%commit_message%"=="" set commit_message=Update cybersecurity portfolio website

git commit -m "%commit_message%"

echo.
echo Checking if remote repository exists...
git remote get-url origin >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  No remote repository configured
    echo Please add your GitHub repository URL:
    echo git remote add origin https://github.com/yourusername/artofvector.git
    echo.
    echo Then run this script again
    pause
    exit /b 1
)

echo.
echo Pushing to GitHub...
git push origin main

if %errorlevel% equ 0 (
    echo.
    echo ✅ Successfully deployed to GitHub!
    echo.
    echo Your website will be available at:
    echo https://yourusername.github.io/artofvector
    echo.
    echo Don't forget to enable GitHub Pages in your repository settings!
) else (
    echo.
    echo ❌ Failed to push to GitHub
    echo Please check your credentials and try again
)

echo.
pause
