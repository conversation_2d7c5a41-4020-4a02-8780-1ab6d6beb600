import type { Metadata } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ArtOfVector - Cybersecurity & Penetration Testing",
  description: "Professional cybersecurity specialist, penetration tester, and ethical hacker. Explore my writeups, tools, and security research.",
  keywords: ["cybersecurity", "penetration testing", "ethical hacking", "security research", "CTF", "writeups"],
  authors: [{ name: "ArtOfVector" }],
  openGraph: {
    title: "ArtOfVector - Cybersecurity & Penetration Testing",
    description: "Professional cybersecurity specialist and penetration tester",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased bg-black text-cyan-400 min-h-screen`}
      >
        <Navigation />
        <main className="relative">
          {children}
        </main>
      </body>
    </html>
  );
}
