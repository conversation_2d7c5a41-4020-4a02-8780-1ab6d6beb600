{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/artofvector/src/components/Hero3D.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { Points, PointMaterial } from '@react-three/drei';\nimport * as THREE from 'three';\n\nfunction AnimatedPoints() {\n  const ref = useRef<THREE.Points>(null);\n  \n  // Generate random points for the matrix effect\n  const particlesPosition = new Float32Array(5000 * 3);\n  \n  for (let i = 0; i < 5000; i++) {\n    const x = (Math.random() - 0.5) * 10;\n    const y = (Math.random() - 0.5) * 10;\n    const z = (Math.random() - 0.5) * 10;\n    \n    particlesPosition.set([x, y, z], i * 3);\n  }\n\n  useFrame((state) => {\n    if (ref.current) {\n      ref.current.rotation.x = state.clock.elapsedTime * 0.1;\n      ref.current.rotation.y = state.clock.elapsedTime * 0.05;\n    }\n  });\n\n  return (\n    <Points ref={ref} positions={particlesPosition} stride={3} frustumCulled={false}>\n      <PointMaterial\n        transparent\n        color=\"#ff4444\"\n        size={0.02}\n        sizeAttenuation={true}\n        depthWrite={false}\n      />\n    </Points>\n  );\n}\n\nfunction FloatingCube() {\n  const meshRef = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.x = state.clock.elapsedTime * 0.5;\n      meshRef.current.rotation.y = state.clock.elapsedTime * 0.3;\n      meshRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.5;\n    }\n  });\n\n  return (\n    <mesh ref={meshRef} position={[0, 0, 0]}>\n      <boxGeometry args={[1, 1, 1]} />\n      <meshStandardMaterial\n        color=\"#ff4444\"\n        transparent\n        opacity={0.3}\n        wireframe\n      />\n    </mesh>\n  );\n}\n\nconst Hero3D = () => {\n  return (\n    <div className=\"absolute inset-0 w-full h-full\">\n      <Canvas\n        camera={{ position: [0, 0, 5], fov: 75 }}\n        style={{ background: 'transparent' }}\n      >\n        <ambientLight intensity={0.5} />\n        <pointLight position={[10, 10, 10]} />\n        <AnimatedPoints />\n        <FloatingCube />\n      </Canvas>\n    </div>\n  );\n};\n\nexport default Hero3D;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAJA;;;;;AAOA,SAAS;IACP,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAgB;IAEjC,+CAA+C;IAC/C,MAAM,oBAAoB,IAAI,aAAa,OAAO;IAElD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;QAC7B,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAClC,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAClC,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAElC,kBAAkB,GAAG,CAAC;YAAC;YAAG;YAAG;SAAE,EAAE,IAAI;IACvC;IAEA,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,IAAI,OAAO,EAAE;YACf,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACnD,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;QACrD;IACF;IAEA,qBACE,8OAAC,0JAAA,CAAA,SAAM;QAAC,KAAK;QAAK,WAAW;QAAmB,QAAQ;QAAG,eAAe;kBACxE,cAAA,8OAAC,iKAAA,CAAA,gBAAa;YACZ,WAAW;YACX,OAAM;YACN,MAAM;YACN,iBAAiB;YACjB,YAAY;;;;;;;;;;;AAIpB;AAEA,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IAEnC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACvD,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,WAAW,GAAG;YACvD,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI;QACnE;IACF;IAEA,qBACE,8OAAC;QAAK,KAAK;QAAS,UAAU;YAAC;YAAG;YAAG;SAAE;;0BACrC,8OAAC;gBAAY,MAAM;oBAAC;oBAAG;oBAAG;iBAAE;;;;;;0BAC5B,8OAAC;gBACC,OAAM;gBACN,WAAW;gBACX,SAAS;gBACT,SAAS;;;;;;;;;;;;AAIjB;AAEA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,mMAAA,CAAA,SAAM;YACL,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,KAAK;YAAG;YACvC,OAAO;gBAAE,YAAY;YAAc;;8BAEnC,8OAAC;oBAAa,WAAW;;;;;;8BACzB,8OAAC;oBAAW,UAAU;wBAAC;wBAAI;wBAAI;qBAAG;;;;;;8BAClC,8OAAC;;;;;8BACD,8OAAC;;;;;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/artofvector/src/components/TypingEffect.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface TypingEffectProps {\n  text: string;\n  speed?: number;\n  className?: string;\n}\n\nconst TypingEffect = ({ text, speed = 100, className = '' }: TypingEffectProps) => {\n  const [displayText, setDisplayText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  useEffect(() => {\n    if (currentIndex < text.length) {\n      const timeout = setTimeout(() => {\n        setDisplayText(prev => prev + text[currentIndex]);\n        setCurrentIndex(prev => prev + 1);\n      }, speed);\n\n      return () => clearTimeout(timeout);\n    }\n  }, [currentIndex, text, speed]);\n\n  return (\n    <span className={className}>\n      {displayText}\n      <span className=\"animate-pulse\">|</span>\n    </span>\n  );\n};\n\nexport default TypingEffect;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUA,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,QAAQ,GAAG,EAAE,YAAY,EAAE,EAAqB;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,KAAK,MAAM,EAAE;YAC9B,MAAM,UAAU,WAAW;gBACzB,eAAe,CAAA,OAAQ,OAAO,IAAI,CAAC,aAAa;gBAChD,gBAAgB,CAAA,OAAQ,OAAO;YACjC,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAc;QAAM;KAAM;IAE9B,qBACE,8OAAC;QAAK,WAAW;;YACd;0BACD,8OAAC;gBAAK,WAAU;0BAAgB;;;;;;;;;;;;AAGtC;uCAEe", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/GitHub/artofvector/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense } from 'react';\nimport { motion } from 'framer-motion';\nimport { Terminal, Shield, Code, Zap } from 'lucide-react';\nimport Hero3D from '@/components/Hero3D';\nimport TypingEffect from '@/components/TypingEffect';\n\nexport default function Home() {\n  const skills = [\n    { icon: Shield, title: 'Penetration Testing', description: 'Advanced security assessments and vulnerability analysis' },\n    { icon: Code, title: 'Exploit Development', description: 'Custom exploit creation and security research' },\n    { icon: Terminal, title: 'Red Team Operations', description: 'Simulated attacks and security validation' },\n    { icon: Zap, title: 'CTF Competitions', description: 'Capture The Flag challenges and writeups' },\n  ];\n\n  return (\n    <div className=\"min-h-screen pt-16\">\n      {/* Hero Section with 3D Background */}\n      <section className=\"relative h-screen flex items-center justify-center overflow-hidden\">\n        <Suspense fallback={<div className=\"absolute inset-0 bg-black\" />}>\n          <Hero3D />\n        </Suspense>\n        \n        <div className=\"relative z-10 text-center max-w-4xl mx-auto px-4\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 1 }}\n            className=\"space-y-6\"\n          >\n            <h1 className=\"text-5xl md:text-7xl font-bold matrix-text glitch\">\n              ArtOfVector\n            </h1>\n            \n            <div className=\"text-xl md:text-2xl text-red-300 font-mono\">\n              <TypingEffect\n                text=\"Red Team Operator | Penetration Tester | Offensive Security\"\n                speed={50}\n              />\n            </div>\n\n            <p className=\"text-lg text-red-200 max-w-2xl mx-auto leading-relaxed\">\n              Welcome to my digital realm. I specialize in cybersecurity, penetration testing, \n              and ethical hacking. Explore my latest security research, CTF writeups, and tools.\n            </p>\n            \n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 2, duration: 1 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mt-8\"\n            >\n              <motion.a\n                href=\"/blog\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-8 py-3 bg-red-400 text-black font-semibold rounded-lg hover:bg-red-300 transition-colors neon-glow\"\n              >\n                View Writeups\n              </motion.a>\n\n              <motion.a\n                href=\"/about\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-8 py-3 border border-red-400 text-red-400 font-semibold rounded-lg hover:bg-red-400/10 transition-colors\"\n              >\n                About Me\n              </motion.a>\n            </motion.div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Skills Section */}\n      <section className=\"py-20 px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl font-bold matrix-text mb-4\">Expertise</h2>\n            <p className=\"text-red-300 text-lg\">Specialized skills in red team operations and offensive security</p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {skills.map((skill, index) => {\n              const Icon = skill.icon;\n              return (\n                <motion.div\n                  key={skill.title}\n                  initial={{ opacity: 0, y: 50 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.8, delay: index * 0.1 }}\n                  whileHover={{ y: -10 }}\n                  className=\"bg-red-400/5 border border-red-400/20 rounded-lg p-6 text-center hover:border-red-400/40 transition-all duration-300\"\n                >\n                  <div className=\"inline-flex items-center justify-center w-16 h-16 bg-red-400/10 rounded-full mb-4\">\n                    <Icon className=\"w-8 h-8 text-red-400\" />\n                  </div>\n                  <h3 className=\"text-xl font-semibold text-red-400 mb-2\">{skill.title}</h3>\n                  <p className=\"text-red-300 text-sm\">{skill.description}</p>\n                </motion.div>\n              );\n            })}\n          </div>\n        </div>\n      </section>\n\n      {/* Latest Posts Preview */}\n      <section className=\"py-20 px-4 bg-red-400/5\">\n        <div className=\"max-w-6xl mx-auto\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-4xl font-bold matrix-text mb-4\">Latest Writeups</h2>\n            <p className=\"text-red-300 text-lg\">Recent red team operations and attack techniques</p>\n          </motion.div>\n\n          <div className=\"text-center\">\n            <motion.a\n              href=\"/blog\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"inline-flex items-center px-6 py-3 border border-red-400 text-red-400 font-semibold rounded-lg hover:bg-red-400/10 transition-colors\"\n            >\n              <Terminal className=\"w-5 h-5 mr-2\" />\n              Explore All Posts\n            </motion.a>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS;QACb;YAAE,MAAM,sMAAA,CAAA,SAAM;YAAE,OAAO;YAAuB,aAAa;QAA2D;QACtH;YAAE,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO;YAAuB,aAAa;QAAgD;QACzG;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,OAAO;YAAuB,aAAa;QAA4C;QACzG;YAAE,MAAM,gMAAA,CAAA,MAAG;YAAE,OAAO;YAAoB,aAAa;QAA2C;KACjG;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC,qMAAA,CAAA,WAAQ;wBAAC,wBAAU,8OAAC;4BAAI,WAAU;;;;;;kCACjC,cAAA,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;kCAGT,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAE;4BAC1B,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAIlE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;wCACX,MAAK;wCACL,OAAO;;;;;;;;;;;8CAIX,8OAAC;oCAAE,WAAU;8CAAyD;;;;;;8CAKtE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;wCAAG,UAAU;oCAAE;oCACpC,WAAU;;sDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAK;4CACL,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;sDACX;;;;;;sDAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAK;4CACL,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAuB;;;;;;;;;;;;sCAGtC,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO;gCAClB,MAAM,OAAO,MAAM,IAAI;gCACvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,YAAY;wCAAE,GAAG,CAAC;oCAAG;oCACrB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDAA2C,MAAM,KAAK;;;;;;sDACpE,8OAAC;4CAAE,WAAU;sDAAwB,MAAM,WAAW;;;;;;;mCAXjD,MAAM,KAAK;;;;;4BActB;;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAuB;;;;;;;;;;;;sCAGtC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,MAAK;gCACL,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD", "debugId": null}}]}